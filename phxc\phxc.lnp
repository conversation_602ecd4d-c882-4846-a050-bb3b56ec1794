--cpu Cortex-M3
"phxc\startup_stm32f103xb.o"
"phxc\main.o"
"phxc\gpio.o"
"phxc\tim.o"
"phxc\usart.o"
"phxc\stm32f1xx_it.o"
"phxc\stm32f1xx_hal_msp.o"
"phxc\stm32f1xx_hal_gpio_ex.o"
"phxc\stm32f1xx_hal_tim.o"
"phxc\stm32f1xx_hal_tim_ex.o"
"phxc\stm32f1xx_hal.o"
"phxc\stm32f1xx_hal_rcc.o"
"phxc\stm32f1xx_hal_rcc_ex.o"
"phxc\stm32f1xx_hal_gpio.o"
"phxc\stm32f1xx_hal_dma.o"
"phxc\stm32f1xx_hal_cortex.o"
"phxc\stm32f1xx_hal_pwr.o"
"phxc\stm32f1xx_hal_flash.o"
"phxc\stm32f1xx_hal_flash_ex.o"
"phxc\stm32f1xx_hal_exti.o"
"phxc\stm32f1xx_hal_uart.o"
"phxc\system_stm32f1xx.o"
"phxc\oled.o"
"phxc\encoder.o"
"phxc\kalmanfilter.o"
"phxc\mpu6050.o"
"phxc\wheel.o"
"phxc\pid.o"
--library_type=microlib --strict --scatter "phxc\phxc.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "phxc.map" -o phxc\phxc.axf