*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'D:\MDK\ARM\ARMCC\Bin'
Build target 'phxc'
compiling usart.c...
compiling wheel.c...
..\BSP\wheel.h(14): warning:  #1-D: last line of file ends without a newline
  #endif
..\BSP\wheel.c(12): warning:  #188-D: enumerated type mixed with another type
      HAL_GPIO_WritePin(GPIOA, WHEEL_A1, 1);
..\BSP\wheel.c(13): warning:  #188-D: enumerated type mixed with another type
      HAL_GPIO_WritePin(GPIOA, WHEEL_A2, 0);
..\BSP\wheel.c(14): warning:  #188-D: enumerated type mixed with another type
      HAL_GPIO_WritePin(GPIOA, WHEEL_B1, 1);
..\BSP\wheel.c(15): warning:  #188-D: enumerated type mixed with another type
      HAL_GPIO_WritePin(GPIOA, WHEEL_B2, 0);
..\BSP\wheel.c(24): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_A1, 1);
..\BSP\wheel.c(25): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_A2, 0);
..\BSP\wheel.c(26): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_B1, 0);
..\BSP\wheel.c(27): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_B2, 1);
..\BSP\wheel.c(33): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_A1, 0);
..\BSP\wheel.c(34): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_A2, 1);
..\BSP\wheel.c(35): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_B1, 1);
..\BSP\wheel.c(36): warning:  #188-D: enumerated type mixed with another type
          HAL_GPIO_WritePin(GPIOA, WHEEL_B2, 0);
..\BSP\wheel.c: 13 warnings, 0 errors
compiling main.c...
../BSP/MPU6050.h(67): warning:  #1-D: last line of file ends without a newline
  #endif /* __MPU6050_H */
../BSP/wheel.h(14): warning:  #1-D: last line of file ends without a newline
  #endif
../BSP/KalmanFilter.h(75): warning:  #1-D: last line of file ends without a newline
  #endif /* __KALMAN_FILTER_H */
../BSP/encoder.h(31): warning:  #1-D: last line of file ends without a newline
  #endif /* __ENCODER_H */
../Core/Src/main.c(107): warning:  #223-D: function "KalmanFilter_Config" declared implicitly
    KalmanFilter_Config();
../Core/Src/main.c(110): warning:  #177-D: variable "buf"  was declared but never referenced
    char buf[10];
../Core/Src/main.c: 6 warnings, 0 errors
compiling KalmanFilter.c...
..\BSP\KalmanFilter.h(75): warning:  #1-D: last line of file ends without a newline
  #endif /* __KALMAN_FILTER_H */
..\BSP\MPU6050.h(67): warning:  #1-D: last line of file ends without a newline
  #endif /* __MPU6050_H */
..\BSP\KalmanFilter.c(16): warning:  #177-D: variable "kalmanY"  was declared but never referenced
  static KalmanFilter_t kalmanY;
..\BSP\KalmanFilter.c(17): warning:  #177-D: variable "kalmanZ"  was declared but never referenced
  static KalmanFilter_t kalmanZ;
..\BSP\KalmanFilter.c: 4 warnings, 0 errors
compiling MPU6050.c...
..\BSP\MPU6050.h(67): warning:  #1-D: last line of file ends without a newline
  #endif /* __MPU6050_H */
..\BSP\MPU6050.c(16): warning:  #188-D: enumerated type mixed with another type
      HAL_GPIO_WritePin(MPU6050_SCL_PORT, MPU6050_SCL_PIN, state);
..\BSP\MPU6050.c(23): warning:  #188-D: enumerated type mixed with another type
      HAL_GPIO_WritePin(MPU6050_SDA_PORT, MPU6050_SDA_PIN, state);
..\BSP\MPU6050.c(237): warning:  #520-D: initialization with "{...}" expected for aggregate object
      static float sumx[5] = 0;
..\BSP\MPU6050.c(250): warning:  #223-D: function "KalmanFilter_Task" declared implicitly
      KalmanFilter_Task( a.now_x);
..\BSP\MPU6050.c(251): warning:  #223-D: function "Keman_X" declared implicitly
      a.kemn_x = Keman_X();
..\BSP\MPU6050.c(255): warning:  #1-D: last line of file ends without a newline
  }
..\BSP\MPU6050.c: 7 warnings, 0 errors
linking...
Program Size: Code=13024 RO-data=312 RW-data=44 ZI-data=1244  
FromELF: creating hex file...
"phxc\phxc.axf" - 0 Error(s), 30 Warning(s).
Build Time Elapsed:  00:00:11
